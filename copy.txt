$ node trip_planning_example
�️ Mock tools loaded successfully:
  ✈️ Flight Search API
  � Hotel Search API
  �️ Travel Guide/Attractions API
  �️ Restaurant Search API
[dotenv@17.2.1] injecting env (6) from .env -- tip: ⚙️  write to custom object with { processEnv: myObject }

� Testing Individual Mock Tools:

1. Flight Search Test:
✈️ [FLIGHT API] Searching flights with params: {
  origin: 'New York',
  destination: 'Paris',
  departureDate: '2024-06-15',
  returnDate: '2024-06-20',
  passengers: 2,
  class: 'economy',
  budget: 800
}
✈️ [FLIGHT API] Found 7 flight options
   Found 3 outbound flights
   Cheapest outbound: $324

2. Hotel Search Test:
� [HOTEL API] Searching hotels with params: {
  location: 'Paris',
  checkInDate: '2024-06-15',
  checkOutDate: '2024-06-20',
  guests: 2,
  starRating: 4,
  budget: 200
}
� [HOTEL API] Found 1 hotel options
   Found 1 hotels
   Cheapest hotel: $151/night

3. Attractions Search Test:
�️ [TRAVEL GUIDE API] Searching attractions with params: {
  location: 'Paris',
  duration: 5,
  interests: 'art,history',
  budget: 'medium'
}
�️ [TRAVEL GUIDE API] Found 6 attractions for 5 days
   Found 6 attractions
   Daily itineraries: 5 days

4. Restaurant Search Test:
�️ [RESTAURANT API] Searching restaurants with params: {
  location: 'Paris',
  cuisine: 'french',
  priceRange: 'moderate',
  atmosphere: 'romantic',
  maxResults: 3
}
�️ [RESTAURANT API] Found 3 restaurants
   Found 3 restaurants
   Average price: $31

✅ All individual tool tests completed!

============================================================
� FULL AGENCY WORKFLOW TEST
============================================================

� Note: To run the full agency workflow, ensure:
   1. All agent configurations are properly set up
   2. LLM providers are configured with API keys
   3. The AgencyFactory can load the trip.json configuration

�️ Mock tools are ready for integration!
� Trip Planning Agency Example with Mock Tools

ℹ️ [2025-09-08T13:42:09.120Z] INFO: Created agency: Intelligent Trip Planning Agency
ℹ️ [2025-09-08T13:42:09.120Z] INFO: Description: Uses a Planner Module to decompose, sequence, and execute complex trip planning requests.
� [2025-09-08T13:42:09.120Z] DEBUG: Logging level: debug
� [2025-09-08T13:42:09.120Z] DEBUG: Tracing: enabled
Team configuration from createTeamFromConfig:
- teamId: trip-planning-team
- name: Trip Planning Team
- agents: {
  'master-planner': 'master-planner',
  'trip-planner': 'trip-planner',
  'flight-researcher': 'flight-researcher',
  'hotel-booker': 'hotel-booker',
  'itinerary-builder': 'itinerary-builder'
}
- jobs: [
  'create-master-plan',
  'research-flights',
  'book-hotel',
  'build-itinerary'
]
- workflow: [
  'create-master-plan',
  'research-flights',
  'book-hotel',
  'build-itinerary'
]
Team constructor called with:
- name: Trip Planning Team
- agents: [
  'master-planner',
  'trip-planner',
  'flight-researcher',
  'hotel-booker',
  'itinerary-builder'
]
- jobs: [
  'create-master-plan',
  'research-flights',
  'book-hotel',
  'build-itinerary'
]
- workflow: [
  'create-master-plan',
  'research-flights',
  'book-hotel',
  'build-itinerary'
]
Created team:
- name: Trip Planning Team
- agents: [
  'master-planner',
  'trip-planner',
  'flight-researcher',
  'hotel-booker',
  'itinerary-builder'
]
- jobs: [
  'create-master-plan',
  'research-flights',
  'book-hotel',
  'build-itinerary'
]
- workflow: [
  'create-master-plan',
  'research-flights',
  'book-hotel',
  'build-itinerary'
]
✅ Agency loaded: Intelligent Trip Planning Agency
� Description: Uses a Planner Module to decompose, sequence, and execute complex trip planning requests.

� Trip Request:
Plan a 5-day romantic trip to Paris for 2 people.
                    Budget: $3000 total.
                    Departure from New York on June 15, 2024, returning June 20, 2024.
                    Interests: art, history, fine dining, romantic atmosphere.
                    Prefer 4-star hotels and moderate to upscale restaurants.

� Starting trip planning process...

Starting team run for: Trip Planning Team
- inputs: {"prompt":"Plan a 5-day romantic trip to Paris for 2 people. \n                    Budget: $3000 total. \n                    Departure from New York on Ju
ne 15, 2024, returning June 20, 2024.\n                    Interests: art, history, fine dining, romantic atmosphere.\n                    Prefer 4-star hotels and m
oderate to upscale restaurants."}
- context: [ 'agency' ]
� Starting team: Trip Planning Team
⏳ Running job: create-master-plan
� [2025-09-08T13:42:09.125Z] DEBUG: Job create-master-plan assigned to agent master-planner {"jobId":"create-master-plan","assigneeId":"master-planner","assigneeTyp 
e":"agent"}
- Agent: master-planner
- Job input: Analyze the complex trip request, break it down into logical components, and create a comprehensive strategic execution plan with budget allocation and 
coordination strategy.

prompt: Plan a 5-day romantic trip to Paris for 2 people.
                    Budget: $3000 total.
                    Departure from New York on June 15, 2024, returning June 20, 2024.
                    Interests: art, history, fine dining, romantic atmosphere.
                    Prefer 4-star hotels and moderate to upscale restaurants.