{"agency": {"name": "Content Creation Agency", "description": "Demonstrates a multi-agent workflow for content creation", "logging": {"level": "basic", "tracing": true}}, "brief": {"content-creation-brief": {"title": "Content Creation Brief", "overview": "Create a polished blog post based on the provided prompt.", "background": "The content creation process involves research, drafting, and refining to ensure high-quality output.", "objective": "Produce a well-researched, engaging, and polished blog post.", "targetAudience": "General audience interested in music and pop culture."}}, "agents": {"planner-orchestrator": {"id": "planner-orchestrator", "name": "Planner Orchestrator", "description": "Orchestrates the workflow by decomposing the main goal and assigning tasks to other agents.", "role": "You are a master project manager and workflow orchestrator. Your primary role is to take a user's request, break it down into a logical sequence of steps, and actively delegate each step to the most suitable specialist agent using the agent-dispatcher tool. You must not only plan but also execute the plan by dispatching tasks to agents and coordinating their work. Always use the agent-dispatcher tool to delegate tasks to prompt-researcher, writer, and refiner agents.", "goals": ["Decompose a complex request into a sequential plan of action.", "Reason about the flow and dependencies between tasks.", "Use the agent-dispatcher tool to delegate tasks to 'prompt-researcher', 'writer', and 'refiner' agents.", "Execute the plan by dispatching each task and coordinating the workflow.", "Manage the overall workflow from start to finish with active delegation."], "provider": "gemini", "llmConfig": {"model": "gemini-2.5-flash", "temperature": 0.3, "maxOutputTokens": 1024}, "tools": {"agent-dispatcher": "agent-dispatcher"}}, "prompt-researcher": {"id": "prompt-researcher", "name": "Prompt Researcher", "description": "Specializes in finding and analyzing relevant information based on a prompt", "role": "You are a research specialist. Your task is to find relevant information based on the provided prompt. Be thorough in your research and provide structured, actionable insights.", "goals": ["Find comprehensive information based on the prompt", "Provide structured research results"], "provider": "gemini", "llmConfig": {"model": "gemini-2.5-flash", "temperature": 0.4, "maxOutputTokens": 3072}, "tools": {"webSearch": "webSearch"}}, "writer": {"id": "writer", "name": "Writer", "description": "Specializes in drafting content based on research", "role": "You are a content writer. Your task is to draft engaging and informative content based on the research provided. Ensure the content is well-structured and aligned with the prompt.", "goals": ["Draft engaging and informative content", "Ensure the content is well-structured and aligned with the prompt"], "provider": "gemini", "llmConfig": {"model": "gemini-2.5-flash", "temperature": 0.5, "maxOutputTokens": 2048}}, "refiner": {"id": "refiner", "name": "Refiner", "description": "Specializes in refining and polishing content", "role": "You are a content refiner. Your task is to refine and polish the draft to improve clarity, coherence, and quality. Ensure the final content is polished and ready for publication.", "goals": ["Refine and polish the draft for clarity and coherence", "Ensure the final content is polished and ready for publication"], "provider": "gemini", "llmConfig": {"model": "gemini-2.5-flash", "temperature": 0.5, "maxOutputTokens": 2048}}}, "teams": {"content-creation-team": {"name": "Content Creation Team", "description": "Collaborates to create polished content based on a prompt", "agents": {"planner-orchestrator": "planner-orchestrator", "prompt-researcher": "prompt-researcher", "writer": "writer", "refiner": "refiner"}, "jobs": {"plan": {"agent": "planner-orchestrator", "description": "Analyze the request and create a plan for content creation", "inputs": {"prompt": "{{initialInputs.prompt}}"}}}, "workflow": ["plan"]}}}