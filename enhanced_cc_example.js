// enhanced_cc_example.js - Example using the enhanced cc.json with planner-orchestrator
import { AgentFactory } from './AgentFactory.js';
import { Agency } from './Agency.js';
import { Team } from './Team.js';
import { webSearchTool } from './tools/search_tool.js';
import { agentDelegationTools } from './tools/agent_delegation_tools.js';
import ccConfig from './cc.json' assert { type: 'json' };

async function main() {
    console.log('🚀 Enhanced Content Creation Agency with Planner-Orchestrator\n');
    
    // Create the agency
    const agency = new Agency({
        name: ccConfig.agency.name,
        description: ccConfig.agency.description,
        logging: ccConfig.agency.logging
    });

    // Create the agent factory
    const agentFactory = new AgentFactory({
        apiKeys: {
            gemini: process.env.GEMINI_API_KEY,
        }
    });

    // Register tools
    agentFactory.registerTool(webSearchTool);
    agentFactory.registerTool(agentDelegationTools['agent-dispatcher']);

    // Create agents from the enhanced configuration
    for (const [agentId, agentConfig] of Object.entries(ccConfig.agents)) {
        console.log(`🤖 Creating agent: ${agentConfig.name}`);
        
        const agent = agentFactory.createAgent({
            id: agentId,
            name: agentConfig.name,
            description: agentConfig.description,
            role: agentConfig.role,
            goals: agentConfig.goals,
            provider: agentConfig.provider,
            llmConfig: agentConfig.llmConfig,
            tools: agentConfig.tools
        });

        agency.addAgent(agentId, agent);
    }

    // Create the team
    const teamConfig = ccConfig.teams['content-creation-team'];
    const team = new Team({
        id: 'content-creation-team',
        name: teamConfig.name,
        description: teamConfig.description,
        agents: teamConfig.agents
    });

    agency.addTeam('content-creation-team', team);

    console.log('\n📋 Agency Setup Complete!');
    console.log(`✅ Agents created: ${Object.keys(ccConfig.agents).join(', ')}`);
    console.log(`✅ Team created: ${teamConfig.name}`);
    
    // Example interaction with the planner-orchestrator
    console.log('\n🎯 Testing Planner-Orchestrator Agent');
    
    const plannerAgent = agency.getAgent('planner-orchestrator');
    if (plannerAgent) {
        const prompt = "Create a comprehensive blog post about the future of renewable energy technology, including current trends, challenges, and predictions for the next decade.";
        
        console.log(`\n📝 Sending request to planner: "${prompt}"`);
        
        try {
            const response = await plannerAgent.processMessage(prompt);
            console.log('\n🎉 Planner Response:');
            console.log('='.repeat(60));
            console.log(response);
            console.log('='.repeat(60));
        } catch (error) {
            console.error('❌ Error processing message:', error);
        }
    } else {
        console.error('❌ Planner-orchestrator agent not found');
    }

    // Show available tools for each agent
    console.log('\n🔧 Agent Tools Summary:');
    for (const [agentId, agentConfig] of Object.entries(ccConfig.agents)) {
        const tools = agentConfig.tools ? Object.keys(agentConfig.tools) : [];
        console.log(`  ${agentId}: [${tools.join(', ') || 'No tools'}]`);
    }
}

// Handle errors and run
main().catch(error => {
    console.error('❌ Application error:', error);
    process.exit(1);
});
