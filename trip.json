{"agency": {"name": "Intelligent Trip Planning Agency", "description": "Uses a Planner Module to decompose, sequence, and execute complex trip planning requests.", "logging": {"level": "debug", "tracing": true}}, "brief": {"trip-planning-brief": {"title": "Personalized Trip Planning Brief", "overview": "Plan a complete, personalized trip based on user preferences.", "background": "Trip planning requires coordination of research, booking, and scheduling with dependencies.", "objective": "Deliver a complete, executable trip plan with flights, lodging, attractions, and dining.", "targetAudience": "Travelers seeking hassle-free, personalized itineraries."}}, "agents": {"trip-planner": {"id": "trip-planner", "name": "<PERSON>ner", "description": "Master planner that decomposes trip requests into executable steps and manages workflow.", "role": "You are a senior trip planning strategist. Your task is to break down the user's trip request into a detailed, step-by-step plan with clear dependencies. Output a structured JSON plan that includes: steps, assigned agents, required inputs, and dependencies. Do NOT execute tasks — only plan them.", "goals": ["Decompose complex trip requests into atomic, executable steps.", "Define correct sequence and dependencies between steps.", "Assign each step to the appropriate specialized agent.", "Output plan in strict JSON format for machine execution."], "provider": "gemini", "llmConfig": {"model": "gemini-2.5-pro", "temperature": 0.3, "maxOutputTokens": 4096}, "outputSchema": {"type": "object", "properties": {"plan": {"type": "array", "items": {"type": "object", "properties": {"stepId": {"type": "string"}, "description": {"type": "string"}, "assignedAgent": {"type": "string"}, "requiredInputs": {"type": "array", "items": {"type": "string"}}, "dependsOn": {"type": "array", "items": {"type": "string"}}, "tool": {"type": "string"}}, "required": ["stepId", "description", "assignedAgent"]}}}, "required": ["plan"]}, "tools": {"plan_and_execute": "planner_tool", "delegate_flight_research": "flight-researcher", "delegate_hotel_booking": "hotel-booker", "delegate_itinerary_building": "itinerary-builder", "reasoning": "reasoning_tool"}}, "master-planner": {"id": "master-planner", "name": "Master Travel Planner", "description": "Analyzes complex travel requests, breaks them down into logical sequences, and coordinates with specialized agents to execute comprehensive trip plans.", "role": "You are an expert travel planner and coordinator. Your primary function is to analyze complex user requests and break them down into a logical, sequential plan. You must identify the necessary sub-tasks, determine the correct approach for each component, and create a comprehensive execution strategy. Always think step-by-step: 1) Analyze the request thoroughly, 2) Identify constraints and priorities, 3) Break down into actionable components, 4) Create a detailed execution plan with budget allocation, 5) Provide clear guidance for implementation. You coordinate the overall strategy while other specialists handle specific tasks.", "goals": ["Deconstruct complex travel requests into actionable steps", "Analyze constraints (budget, time, preferences) and create realistic strategies", "Determine the most efficient sequence of actions to achieve the goal", "Create detailed execution plans with budget allocation and timelines", "Provide strategic guidance and coordination for the entire trip planning process"], "provider": "gemini", "llmConfig": {"model": "gemini-2.5-flash", "temperature": 0.3, "maxOutputTokens": 4096}, "tools": {"scratchpad": "reasoning_tool", "calculator": "calculator"}}, "flight-researcher": {"id": "flight-researcher", "name": "Flight Researcher", "description": "Specialized agent that finds and compares flight options based on specific criteria provided by the master planner.", "role": "You are a flight specialist working under the guidance of a master planner. Search for flights matching the specific criteria provided. Focus on finding the best options within the given constraints and return structured, comparable results with detailed analysis.", "goals": ["Find optimal flight options based on provided criteria", "Analyze and compare flight options for value and convenience", "Return structured, detailed flight information with recommendations"], "provider": "gemini", "llmConfig": {"model": "gemini-2.5-flash", "temperature": 0.4, "maxOutputTokens": 2048}, "tools": {"flightSearch": "flight_api"}}, "hotel-booker": {"id": "hotel-booker", "name": "Hotel Booker", "description": "Finds and books hotels based on location, dates, budget, and preferences.", "role": "You are a hotel booking specialist. Find and suggest hotels matching criteria. Use exact dates from flight results.", "goals": ["Find suitable hotels near destination", "Ensure dates and budget align with flight plan"], "provider": "gemini", "llmConfig": {"model": "gemini-2.5-flash", "temperature": 0.5, "maxOutputTokens": 2048}, "tools": {"hotelSearch": "hotel_api"}}, "itinerary-builder": {"id": "itinerary-builder", "name": "Itinerary Builder", "description": "Creates a day-by-day itinerary including attractions, transport, and dining.", "role": "You are an itinerary designer. Use hotel location and trip dates to build a daily schedule. Include top attractions and nearby restaurants.", "goals": ["Create engaging, realistic daily schedule", "Include location-aware restaurant suggestions"], "provider": "gemini", "llmConfig": {"model": "gemini-2.5-flash", "temperature": 0.7, "maxOutputTokens": 3072}, "tools": {"attractionSearch": "travel_guide_api", "restaurantSearch": "restaurant_api"}}}, "teams": {"trip-planning-team": {"name": "Trip Planning Team", "description": "Orchestrates end-to-end trip planning using a Planner-led workflow.", "agents": {"master-planner": "master-planner", "trip-planner": "trip-planner", "flight-researcher": "flight-researcher", "hotel-booker": "hotel-booker", "itinerary-builder": "itinerary-builder"}, "jobs": {"create-master-plan": {"agent": "master-planner", "description": "Analyze the complex trip request, break it down into logical components, and create a comprehensive strategic execution plan with budget allocation and coordination strategy.", "inputs": {"prompt": "{{initialInputs.prompt}}"}}, "research-flights": {"agent": "flight-researcher", "description": "Execute flight research based on the master plan's specifications and constraints.", "inputs": {"prompt": "{{initialInputs.prompt}}", "masterPlan": "{{create-master-plan}}"}}, "book-hotel": {"agent": "hotel-booker", "description": "Find and suggest hotel options following the master plan's accommodation strategy.", "inputs": {"prompt": "{{initialInputs.prompt}}", "masterPlan": "{{create-master-plan}}", "flights": "{{research-flights}}"}}, "build-itinerary": {"agent": "itinerary-builder", "description": "Create a detailed daily itinerary following the master plan's framework and incorporating all booked components.", "inputs": {"prompt": "{{initialInputs.prompt}}", "masterPlan": "{{create-master-plan}}", "flights": "{{research-flights}}", "hotel": "{{book-hotel}}"}}}, "workflow": ["create-master-plan", "research-flights", "book-hotel", "build-itinerary"]}}}