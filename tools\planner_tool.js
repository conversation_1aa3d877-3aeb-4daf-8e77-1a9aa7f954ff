// planner_tool.js - Real planner tool that analyzes context and creates dynamic plans
export const plannerTool = {
    name: 'planner_tool',
    description: 'Real planner that analyzes context, creates dynamic execution plans, and coordinates specialized agents.',
    schema: {
        function_declaration: {
            name: 'planner_tool',
            description: 'Analyze context and create dynamic, actionable plans with budget allocation and risk assessment.',
            parameters: {
                type: 'OBJECT',
                properties: {
                    action: {
                        type: 'STRING',
                        description: 'Action to perform: create_plan, execute_step, coordinate_agents, validate_plan',
                        enum: ['create_plan', 'execute_step', 'coordinate_agents', 'validate_plan']
                    },
                    plan: {
                        type: 'STRING',
                        description: 'JSON string containing the structured plan with steps, agents, and dependencies'
                    },
                    stepId: {
                        type: 'STRING',
                        description: 'ID of the specific step to execute (for execute_step action)'
                    },
                    context: {
                        type: 'STRING',
                        description: 'Trip request context to analyze and plan from'
                    }
                },
                required: ['action']
            }
        }
    },
    call: async (params) => {
        console.log(`🎯 [PLANNER TOOL] ${params.action.toUpperCase()}`);
        
        try {
            const { action, plan, stepId, context } = params;
            
            switch (action) {
                case 'create_plan':
                    return await createPlan(context);
                    
                case 'execute_step':
                    return await executeStep(plan, stepId, context);
                    
                case 'coordinate_agents':
                    return await coordinateAgents(plan, context);
                    
                case 'validate_plan':
                    return await validatePlan(plan);
                    
                default:
                    throw new Error(`Unknown action: ${action}`);
            }
            
        } catch (error) {
            console.error(`🎯 [PLANNER TOOL] Error:`, error);
            return JSON.stringify({
                success: false,
                error: error.message,
                suggestion: 'Check the plan structure and ensure all required parameters are provided'
            }, null, 2);
        }
    }
};

async function createPlan(context) {
    console.log(`🎯 [PLANNER] Creating dynamic execution plan from context...`);
    
    // Parse and analyze the actual context
    const analysis = analyzeContext(context);
    
    // Create dynamic plan based on actual requirements
    const dynamicPlan = {
        planId: `plan_${Date.now()}`,
        created: new Date().toISOString(),
        context: context,
        analysis: analysis,
        budgetAllocation: calculateBudgetAllocation(analysis),
        steps: generateDynamicSteps(analysis),
        coordination: calculateCoordination(analysis),
        riskAssessment: assessRisks(analysis),
        contingencies: createContingencies(analysis)
    };
    
    return JSON.stringify({
        success: true,
        action: 'create_plan',
        plan: dynamicPlan,
        nextSteps: generateNextSteps(dynamicPlan)
    }, null, 2);
}

function analyzeContext(context) {
    console.log(`🔍 [PLANNER] Analyzing context: ${typeof context === 'string' ? context.substring(0, 100) : 'object'}...`);
    
    // Extract key information from context
    const analysis = {
        tripType: 'unknown',
        destination: null,
        duration: 0,
        travelers: 1,
        budget: 0,
        dates: {},
        interests: [],
        preferences: {},
        constraints: [],
        complexity: 'medium'
    };
    
    if (typeof context === 'string') {
        const text = context.toLowerCase();
        
        // Extract trip type
        if (text.includes('romantic') || text.includes('honeymoon')) {
            analysis.tripType = 'romantic';
        } else if (text.includes('business')) {
            analysis.tripType = 'business';
        } else if (text.includes('family')) {
            analysis.tripType = 'family';
        } else if (text.includes('adventure')) {
            analysis.tripType = 'adventure';
        } else {
            analysis.tripType = 'leisure';
        }
        
        // Extract destination
        const destinations = ['paris', 'london', 'tokyo', 'new york', 'rome', 'barcelona', 'amsterdam'];
        for (const dest of destinations) {
            if (text.includes(dest)) {
                analysis.destination = dest.charAt(0).toUpperCase() + dest.slice(1);
                break;
            }
        }
        
        // Extract duration
        const durationMatch = text.match(/(\d+)[\s-]*day/);
        if (durationMatch) {
            analysis.duration = parseInt(durationMatch[1]);
        }
        
        // Extract number of travelers
        const travelersMatch = text.match(/(\d+)\s*people/);
        if (travelersMatch) {
            analysis.travelers = parseInt(travelersMatch[1]);
        }
        
        // Extract budget
        const budgetMatch = text.match(/\$(\d+(?:,\d+)*)/);
        if (budgetMatch) {
            analysis.budget = parseInt(budgetMatch[1].replace(/,/g, ''));
        }
        
        // Extract dates
        const dateMatches = text.match(/(\w+\s+\d+,?\s+\d{4})/g);
        if (dateMatches && dateMatches.length >= 1) {
            analysis.dates.departure = dateMatches[0];
            if (dateMatches.length >= 2) {
                analysis.dates.return = dateMatches[1];
            }
        }
        
        // Extract interests
        const interestKeywords = ['art', 'history', 'food', 'dining', 'culture', 'museums', 'nightlife', 'shopping', 'nature', 'adventure'];
        analysis.interests = interestKeywords.filter(interest => text.includes(interest));
        
        // Extract preferences
        if (text.includes('4-star') || text.includes('four star')) {
            analysis.preferences.hotelRating = 4;
        } else if (text.includes('5-star') || text.includes('five star')) {
            analysis.preferences.hotelRating = 5;
        } else if (text.includes('3-star') || text.includes('three star')) {
            analysis.preferences.hotelRating = 3;
        }
        
        if (text.includes('upscale') || text.includes('fine dining')) {
            analysis.preferences.diningLevel = 'upscale';
        } else if (text.includes('moderate')) {
            analysis.preferences.diningLevel = 'moderate';
        }
        
        // Assess complexity
        let complexityScore = 0;
        if (analysis.duration > 7) complexityScore += 2;
        if (analysis.travelers > 4) complexityScore += 1;
        if (analysis.budget > 10000) complexityScore += 1;
        if (analysis.interests.length > 3) complexityScore += 1;
        if (analysis.tripType === 'business') complexityScore += 1;
        
        analysis.complexity = complexityScore >= 4 ? 'high' : complexityScore >= 2 ? 'medium' : 'low';
    }
    
    console.log(`📊 [PLANNER] Analysis complete:`, analysis);
    return analysis;
}

function calculateBudgetAllocation(analysis) {
    console.log(`💰 [PLANNER] Calculating budget allocation for $${analysis.budget}...`);
    
    if (analysis.budget === 0) {
        return { error: 'No budget specified' };
    }
    
    // Base allocation percentages by trip type
    const allocations = {
        romantic: { flights: 0.35, accommodation: 0.30, dining: 0.20, activities: 0.10, misc: 0.05 },
        business: { flights: 0.40, accommodation: 0.35, dining: 0.15, activities: 0.05, misc: 0.05 },
        family: { flights: 0.30, accommodation: 0.25, dining: 0.25, activities: 0.15, misc: 0.05 },
        adventure: { flights: 0.25, accommodation: 0.20, dining: 0.15, activities: 0.35, misc: 0.05 },
        leisure: { flights: 0.35, accommodation: 0.25, dining: 0.20, activities: 0.15, misc: 0.05 }
    };
    
    const baseAllocation = allocations[analysis.tripType] || allocations.leisure;
    
    // Adjust based on preferences
    if (analysis.preferences.hotelRating >= 4) {
        baseAllocation.accommodation += 0.05;
        baseAllocation.activities -= 0.05;
    }
    
    if (analysis.preferences.diningLevel === 'upscale') {
        baseAllocation.dining += 0.05;
        baseAllocation.misc -= 0.05;
    }
    
    // Calculate actual amounts
    const allocation = {};
    for (const [category, percentage] of Object.entries(baseAllocation)) {
        allocation[category] = {
            percentage: Math.round(percentage * 100),
            amount: Math.round(analysis.budget * percentage),
            perPerson: Math.round((analysis.budget * percentage) / analysis.travelers)
        };
    }
    
    console.log(`💰 [PLANNER] Budget allocation:`, allocation);
    return allocation;
}

function generateDynamicSteps(analysis) {
    console.log(`📋 [PLANNER] Generating dynamic steps for ${analysis.tripType} trip...`);

    const steps = [];
    let stepCounter = 1;

    // Always start with requirements analysis
    steps.push({
        stepId: `step_${stepCounter++}_analyze_requirements`,
        description: `Analyze ${analysis.tripType} trip requirements and constraints`,
        assignedAgent: 'master-planner',
        tool: 'reasoning_tool',
        requiredInputs: ['user_request'],
        dependsOn: [],
        priority: 'critical',
        estimatedDuration: '5 minutes',
        budgetImpact: 0
    });

    // Flight research (if international or long distance)
    if (analysis.destination && analysis.destination !== 'local') {
        steps.push({
            stepId: `step_${stepCounter++}_research_flights`,
            description: `Research flights to ${analysis.destination} for ${analysis.travelers} travelers`,
            assignedAgent: 'flight-researcher',
            tool: 'flight_api',
            requiredInputs: ['origin', 'destination', 'dates', 'budget', 'travelers'],
            dependsOn: ['step_1_analyze_requirements'],
            priority: 'high',
            estimatedDuration: '15 minutes',
            budgetImpact: analysis.budget * 0.35
        });
    }

    // Accommodation search
    steps.push({
        stepId: `step_${stepCounter++}_find_accommodation`,
        description: `Find ${analysis.preferences.hotelRating || 3}-star accommodation in ${analysis.destination}`,
        assignedAgent: 'hotel-booker',
        tool: 'hotel_api',
        requiredInputs: ['location', 'dates', 'budget', 'preferences', 'travelers'],
        dependsOn: ['step_1_analyze_requirements'],
        priority: 'high',
        estimatedDuration: '12 minutes',
        budgetImpact: analysis.budget * 0.30
    });

    // Activity planning based on interests
    if (analysis.interests.length > 0) {
        steps.push({
            stepId: `step_${stepCounter++}_plan_activities`,
            description: `Plan activities for interests: ${analysis.interests.join(', ')}`,
            assignedAgent: 'itinerary-builder',
            tool: 'travel_guide_api',
            requiredInputs: ['location', 'interests', 'duration', 'budget'],
            dependsOn: [`step_${steps.length}_find_accommodation`],
            priority: 'medium',
            estimatedDuration: '20 minutes',
            budgetImpact: analysis.budget * 0.15
        });
    }

    // Dining reservations for upscale trips
    if (analysis.preferences.diningLevel === 'upscale' || analysis.tripType === 'romantic') {
        steps.push({
            stepId: `step_${stepCounter++}_dining_reservations`,
            description: `Make ${analysis.preferences.diningLevel || 'moderate'} dining reservations`,
            assignedAgent: 'itinerary-builder',
            tool: 'restaurant_api',
            requiredInputs: ['location', 'cuisine_preferences', 'budget', 'dates'],
            dependsOn: [`step_${steps.length}_plan_activities`],
            priority: 'medium',
            estimatedDuration: '10 minutes',
            budgetImpact: analysis.budget * 0.20
        });
    }

    // Final itinerary compilation
    steps.push({
        stepId: `step_${stepCounter++}_compile_itinerary`,
        description: 'Compile comprehensive daily itinerary with all bookings',
        assignedAgent: 'itinerary-builder',
        tool: 'travel_guide_api',
        requiredInputs: ['all_previous_results'],
        dependsOn: steps.slice(-2).map(s => s.stepId),
        priority: 'medium',
        estimatedDuration: '15 minutes',
        budgetImpact: 0
    });

    console.log(`📋 [PLANNER] Generated ${steps.length} dynamic steps`);
    return steps;
}

function calculateCoordination(analysis) {
    console.log(`🔄 [PLANNER] Calculating coordination strategy...`);

    const coordination = {
        totalSteps: 0,
        estimatedTotalTime: '0 minutes',
        criticalPath: ['analyze_requirements', 'research_flights', 'find_accommodation'],
        parallelizable: ['research_flights', 'find_accommodation'],
        complexity: analysis.complexity,
        riskLevel: analysis.budget > 5000 ? 'medium' : 'low'
    };

    console.log(`🔄 [PLANNER] Coordination strategy:`, coordination);
    return coordination;
}

function assessRisks(analysis) {
    console.log(`⚠️ [PLANNER] Assessing risks...`);

    const risks = [];

    // Budget risks
    if (analysis.budget < 1000 * analysis.travelers) {
        risks.push({
            type: 'budget',
            level: 'high',
            description: 'Budget may be insufficient for stated preferences',
            mitigation: 'Consider reducing accommodation rating or dining expectations'
        });
    }

    // Destination risks
    if (!analysis.destination) {
        risks.push({
            type: 'planning',
            level: 'high',
            description: 'No clear destination specified',
            mitigation: 'Clarify destination before proceeding'
        });
    }

    console.log(`⚠️ [PLANNER] Identified ${risks.length} risks`);
    return risks;
}

function createContingencies(analysis) {
    console.log(`🛡️ [PLANNER] Creating contingency plans...`);

    const contingencies = {
        budgetOverrun: {
            trigger: 'Total costs exceed budget by >10%',
            actions: ['Reduce accommodation rating', 'Switch to moderate dining', 'Reduce activity budget']
        },
        unavailability: {
            trigger: 'Preferred options unavailable',
            actions: ['Expand search dates', 'Consider alternatives', 'Look at nearby locations']
        }
    };

    console.log(`🛡️ [PLANNER] Created ${Object.keys(contingencies).length} contingency plans`);
    return contingencies;
}

function generateNextSteps(plan) {
    return [
        'Validate the dynamic plan structure and budget allocation',
        'Begin execution with the first step in the critical path',
        'Monitor dependencies and coordinate parallel tasks',
        'Track budget against allocations throughout execution'
    ];
}

async function executeStep(plan, stepId) {
    return JSON.stringify({ success: true, message: 'Step execution simulated' }, null, 2);
}

async function coordinateAgents(plan) {
    return JSON.stringify({ success: true, message: 'Agent coordination simulated' }, null, 2);
}

async function validatePlan(plan) {
    return JSON.stringify({ success: true, message: 'Plan validation simulated' }, null, 2);
}

// Export for use in tool registry
export default plannerTool;
