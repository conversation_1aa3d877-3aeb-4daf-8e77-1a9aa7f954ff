// mock_tools.js - Export all mock tools for trip planning
import { flightSearchTool } from './flight_api.js';
import { hotelSearchTool } from './hotel_api.js';
import { attractionSearchTool } from './travel_guide_api.js';
import { restaurantSearchTool } from './restaurant_api.js';
import { reasoningTool } from './reasoning_tool.js';
import { plannerTool } from './planner_tool.js';
import { agentDelegationTools } from './agent_delegation_tools.js';

// Export all mock tools
export const mockTools = {
    flight_api: flightSearchTool,
    hotel_api: hotelSearchTool,
    travel_guide_api: attractionSearchTool,
    restaurant_api: restaurantSearchTool,
    reasoning_tool: reasoningTool,
    planner_tool: plannerTool,
    ...agentDelegationTools
};

// Individual exports for convenience
export { flightSearchTool, hotelSearchTool, attractionSearchTool, restaurantSearchTool, reasoningTool, plannerTool, agentDelegationTools };

// Tool registry for dynamic lookup
export const toolRegistry = {
    'flight_api': flightSearchTool,
    'hotel_api': hotelSearchTool,
    'travel_guide_api': attractionSearchTool,
    'restaurant_api': restaurantSearchTool,
    'reasoning_tool': reasoningTool,
    'planner_tool': plannerTool,
    'flight-researcher': agentDelegationTools['flight-researcher'],
    'hotel-booker': agentDelegationTools['hotel-booker'],
    'itinerary-builder': agentDelegationTools['itinerary-builder']
};

console.log('🛠️ Mock tools loaded successfully:');
console.log('  ✈️ Flight Search API');
console.log('  🏨 Hotel Search API');
console.log('  🗺️ Travel Guide/Attractions API');
console.log('  🍽️ Restaurant Search API');
