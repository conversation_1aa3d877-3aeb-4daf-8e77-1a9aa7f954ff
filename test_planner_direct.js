// test_planner_direct.js - Test the planner-orchestrator agent directly
import { AgentFactory } from './AgentFactory.js';
import { webSearchTool } from './tools/search_tool.js';
import { agentDelegationTools } from './tools/agent_delegation_tools.js';
import ccConfig from './cc.json' with { type: 'json' };

async function testPlannerDirect() {
    console.log('🧪 Testing Planner-Orchestrator Agent Directly\n');
    
    // Create the agent factory
    const agentFactory = new AgentFactory({
        apiKeys: {
            gemini: process.env.GEMINI_API_KEY,
        }
    });

    // Register tools
    agentFactory.registerTool(webSearchTool);
    agentFactory.registerTool(agentDelegationTools['agent-dispatcher']);

    // Create the planner-orchestrator agent
    const plannerConfig = ccConfig.agents['planner-orchestrator'];
    console.log('🤖 Creating planner-orchestrator agent...');
    
    const plannerAgent = agentFactory.createAgent({
        id: plannerConfig.id,
        name: plannerConfig.name,
        description: plannerConfig.description,
        role: plannerConfig.role,
        goals: plannerConfig.goals,
        provider: plannerConfig.provider,
        llmConfig: plannerConfig.llmConfig,
        tools: plannerConfig.tools
    });

    console.log('✅ Planner agent created successfully');
    console.log(`📋 Available tools: ${Object.keys(plannerConfig.tools).join(', ')}`);

    // Test the planner with a direct request
    const prompt = "Create a blog post about the latest trends in artificial intelligence. Use the agent-dispatcher tool to delegate tasks to the appropriate agents.";
    
    console.log(`\n📝 Sending request to planner: "${prompt}"`);
    console.log('🎯 Expecting the planner to use the agent-dispatcher tool...\n');
    
    try {
        const response = await plannerAgent.run(prompt);
        console.log('🎉 Planner Response:');
        console.log('='.repeat(80));
        console.log(response);
        console.log('='.repeat(80));
    } catch (error) {
        console.error('❌ Error processing message:', error);
    }
}

// Load environment variables
import dotenv from 'dotenv';
dotenv.config();

// Run the test
testPlannerDirect().catch(console.error);
