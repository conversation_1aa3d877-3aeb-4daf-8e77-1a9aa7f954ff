# Agent Dispatcher Tool Guide

## Overview

The `agent-dispatcher` tool enables planner agents to orchestrate workflows by delegating tasks to specialized agents. This tool is designed for multi-agent systems where a master planner coordinates the work of domain-specific agents.

## Features

- **Dynamic Task Delegation**: Dispatch tasks to specific agents based on their capabilities
- **Workflow Orchestration**: Coordinate sequential tasks with proper input/output flow
- **Priority Management**: Set task priorities for better resource allocation
- **Error Handling**: Validate target agents and provide meaningful error messages
- **Execution Tracking**: Generate unique dispatch IDs and estimated completion times

## Tool Schema

```javascript
{
  name: 'agent-dispatcher',
  description: 'Dispatch tasks to specialized agents in a workflow',
  parameters: {
    targetAgent: {
      type: 'STRING',
      description: 'The ID of the agent to dispatch the task to',
      enum: ['prompt-researcher', 'writer', 'refiner']
    },
    task: {
      type: 'STRING', 
      description: 'The specific task to be performed by the target agent'
    },
    inputs: {
      type: 'OBJECT',
      description: 'Input data required by the target agent',
      properties: {
        prompt: 'STRING',    // For prompt-researcher
        research: 'STRING',  // For writer
        draft: 'STRING'      // For refiner
      }
    },
    priority: {
      type: 'STRING',
      enum: ['low', 'medium', 'high', 'urgent']
    },
    expectedOutput: {
      type: 'STRING',
      description: 'Description of expected output format'
    }
  }
}
```

## Usage Examples

### 1. Research Task Dispatch

```javascript
const researchTask = {
  targetAgent: 'prompt-researcher',
  task: 'research_topic',
  inputs: {
    prompt: 'Find information about renewable energy trends in 2024'
  },
  priority: 'high',
  expectedOutput: 'Comprehensive research report with sources'
};

const result = await agentDispatcher.call(researchTask);
```

### 2. Writing Task Dispatch

```javascript
const writingTask = {
  targetAgent: 'writer',
  task: 'draft_content',
  inputs: {
    research: 'Research findings about renewable energy...'
  },
  priority: 'medium',
  expectedOutput: 'Well-structured blog post draft'
};

const result = await agentDispatcher.call(writingTask);
```

### 3. Refinement Task Dispatch

```javascript
const refinementTask = {
  targetAgent: 'refiner',
  task: 'polish_content',
  inputs: {
    draft: 'Draft blog post about renewable energy...'
  },
  priority: 'low',
  expectedOutput: 'Publication-ready polished content'
};

const result = await agentDispatcher.call(refinementTask);
```

## Response Format

The tool returns a JSON object with dispatch details:

```json
{
  "dispatchId": "dispatch-1234567890-123",
  "targetAgent": "prompt-researcher",
  "task": "research_topic",
  "inputs": { "prompt": "..." },
  "priority": "high",
  "expectedOutput": "Comprehensive research report",
  "status": "dispatched",
  "timestamp": "2024-01-01T12:00:00.000Z",
  "estimatedCompletion": "5-8 minutes",
  "message": "Task successfully dispatched to prompt-researcher."
}
```

## Integration with cc.json

The enhanced `cc.json` includes a `planner-orchestrator` agent that uses this tool:

```json
{
  "agents": {
    "planner-orchestrator": {
      "id": "planner-orchestrator",
      "name": "Planner Orchestrator",
      "role": "Master project manager for workflow coordination",
      "tools": {
        "agent-dispatcher": "agent-dispatcher"
      }
    }
  }
}
```

## Workflow Pattern

1. **Planning Phase**: Planner receives user request
2. **Decomposition**: Breaks down request into sequential tasks
3. **Dispatch**: Uses agent-dispatcher to delegate tasks
4. **Coordination**: Manages flow between agents
5. **Integration**: Combines outputs into final result

## Error Handling

The tool validates target agents and provides clear error messages:

```json
{
  "status": "error",
  "message": "Invalid target agent: invalid-agent. Valid agents are: prompt-researcher, writer, refiner"
}
```

## Testing

Run the test file to verify functionality:

```bash
node test_agent_dispatcher.js
```

## Best Practices

1. **Sequential Planning**: Ensure proper task dependencies
2. **Input Validation**: Verify required inputs for each agent
3. **Priority Setting**: Use appropriate priority levels
4. **Error Recovery**: Handle dispatch failures gracefully
5. **Output Tracking**: Monitor dispatch IDs for debugging

## Extending the Tool

To add support for new agents:

1. Update the `validAgents` array in the tool
2. Add the agent to the enum in the schema
3. Update the `getEstimatedCompletion` function
4. Add appropriate input properties to the schema

This tool enables sophisticated multi-agent orchestration while maintaining clear separation of concerns between planning and execution.
