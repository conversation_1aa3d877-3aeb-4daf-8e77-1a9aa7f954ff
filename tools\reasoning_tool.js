// reasoning_tool.js - Scratchpad/Reasoning Tool for Planning Agents
export const reasoningTool = {
    name: 'reasoning_tool',
    description: 'A scratchpad tool that allows agents to record their reasoning process, break down complex problems, and maintain structured thought processes.',
    schema: {
        function_declaration: {
            name: 'reasoning_tool',
            description: 'Record reasoning steps, analyze problems, and maintain structured thought processes during complex planning tasks.',
            parameters: {
                type: 'OBJECT',
                properties: {
                    action: {
                        type: 'STRING',
                        description: 'Type of reasoning action: analyze, breakdown, note, conclusion, strategy',
                        enum: ['analyze', 'breakdown', 'note', 'conclusion', 'strategy']
                    },
                    subject: {
                        type: 'STRING',
                        description: 'What you are reasoning about (e.g., "budget constraints", "travel dates", "user preferences")'
                    },
                    content: {
                        type: 'STRING',
                        description: 'Your reasoning content, thoughts, analysis, or conclusions'
                    },
                    priority: {
                        type: 'STRING',
                        description: 'Priority level of this reasoning point',
                        enum: ['high', 'medium', 'low']
                    },
                    tags: {
                        type: 'STRING',
                        description: 'Comma-separated tags for categorization (e.g., "budget,constraints,critical")'
                    }
                },
                required: ['action', 'subject', 'content']
            }
        }
    },
    call: async (params) => {
        console.log(`🧠 [REASONING TOOL] ${params.action.toUpperCase()} - ${params.subject}`);
        
        try {
            const { action, subject, content, priority = 'medium', tags = '' } = params;
            
            // Create a structured reasoning entry
            const reasoningEntry = {
                timestamp: new Date().toISOString(),
                action,
                subject,
                content,
                priority,
                tags: tags.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0),
                id: `reasoning_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
            };
            
            // Format the reasoning output based on action type
            let formattedOutput = '';
            
            switch (action) {
                case 'analyze':
                    formattedOutput = `📊 ANALYSIS: ${subject}\n${content}`;
                    break;
                case 'breakdown':
                    formattedOutput = `🔍 BREAKDOWN: ${subject}\n${content}`;
                    break;
                case 'note':
                    formattedOutput = `📝 NOTE: ${subject}\n${content}`;
                    break;
                case 'conclusion':
                    formattedOutput = `✅ CONCLUSION: ${subject}\n${content}`;
                    break;
                case 'strategy':
                    formattedOutput = `🎯 STRATEGY: ${subject}\n${content}`;
                    break;
                default:
                    formattedOutput = `💭 REASONING: ${subject}\n${content}`;
            }
            
            // Add priority indicator
            const priorityIndicator = priority === 'high' ? '🔴' : priority === 'medium' ? '🟡' : '🟢';
            formattedOutput = `${priorityIndicator} ${formattedOutput}`;
            
            // Add tags if present
            if (reasoningEntry.tags.length > 0) {
                formattedOutput += `\n🏷️ Tags: ${reasoningEntry.tags.join(', ')}`;
            }
            
            // Log the reasoning step
            console.log(`🧠 [REASONING] ${formattedOutput}`);
            
            // Return structured response
            const response = {
                success: true,
                reasoningEntry,
                formattedOutput,
                summary: `Recorded ${action} about ${subject} with ${priority} priority`,
                nextSteps: generateNextSteps(action, subject, content)
            };
            
            return JSON.stringify(response, null, 2);
            
        } catch (error) {
            console.error(`🧠 [REASONING TOOL] Error:`, error);
            return JSON.stringify({
                success: false,
                error: error.message,
                suggestion: 'Try simplifying your reasoning request or check the parameters'
            }, null, 2);
        }
    }
};

// Helper function to suggest next steps based on reasoning action
function generateNextSteps(action, subject, content) {
    const suggestions = {
        analyze: [
            'Consider breaking down the analysis into smaller components',
            'Identify key constraints and opportunities',
            'Move to strategy formulation based on analysis'
        ],
        breakdown: [
            'Prioritize the broken-down components',
            'Estimate resources needed for each component',
            'Create a sequence or timeline for execution'
        ],
        note: [
            'Review if this note connects to other reasoning points',
            'Consider if this note requires follow-up analysis',
            'Determine if this note affects the overall strategy'
        ],
        conclusion: [
            'Validate the conclusion against original requirements',
            'Consider implementation steps for this conclusion',
            'Document any assumptions made in reaching this conclusion'
        ],
        strategy: [
            'Break down the strategy into actionable steps',
            'Identify resources and tools needed for execution',
            'Consider potential risks and mitigation strategies'
        ]
    };
    
    return suggestions[action] || [
        'Continue with systematic reasoning',
        'Consider next logical step in the planning process',
        'Review and validate current reasoning chain'
    ];
}

// Export for use in tool registry
export default reasoningTool;
