{"name": "@virtron/agency", "version": "1.4.3", "main": "index.js", "type": "module", "scripts": {"test": "echo \"No tests yet\""}, "keywords": ["agent", "autonomous", "framework", "AI", "agentic", "agency", "virtron"], "author": "<PERSON> II", "license": "MIT", "description": "A framework for building autonomous agents that can perform tasks, manage memory, and interact with tools.", "repository": {"type": "git", "url": "https://github.com/Vince489/Agentic-AI"}, "bugs": {"url": "https://github.com/Vince489/Agentic-AI/issues"}, "dependencies": {"@anthropic-ai/sdk": "^0.59.0", "@google/genai": "^1.13.0", "@mistralai/mistralai": "^1.7.5", "cheerio": "^0.22.0", "dotenv": "^17.2.1", "groq-sdk": "^0.30.0", "luxon": "^3.4.1", "mathjs": "^11.9.0", "open": "^10.2.0", "openai": "^5.12.2"}}