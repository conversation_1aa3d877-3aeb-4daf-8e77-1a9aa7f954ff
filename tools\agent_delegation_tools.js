// agent_delegation_tools.js - Tools for delegating tasks to specialized agents
export const flightResearcherTool = {
    name: 'flight-researcher',
    description: 'Delegate flight research tasks to the specialized flight researcher agent.',
    schema: {
        function_declaration: {
            name: 'flight-researcher',
            description: 'Request the flight researcher agent to find and analyze flight options.',
            parameters: {
                type: 'OBJECT',
                properties: {
                    task: {
                        type: 'STRING',
                        description: 'Specific task for the flight researcher (e.g., "find_flights", "compare_options", "analyze_pricing")'
                    },
                    origin: {
                        type: 'STRING',
                        description: 'Origin city or airport code'
                    },
                    destination: {
                        type: 'STRING',
                        description: 'Destination city or airport code'
                    },
                    departureDate: {
                        type: 'STRING',
                        description: 'Departure date in YYYY-MM-DD format'
                    },
                    returnDate: {
                        type: 'STRING',
                        description: 'Return date in YYYY-MM-DD format (optional for one-way)'
                    },
                    passengers: {
                        type: 'NUMBER',
                        description: 'Number of passengers'
                    },
                    budget: {
                        type: 'NUMBER',
                        description: 'Maximum budget per person'
                    },
                    preferences: {
                        type: 'STRING',
                        description: 'Additional preferences (class, airline, timing, etc.)'
                    }
                },
                required: ['task', 'origin', 'destination', 'departureDate']
            }
        }
    },
    call: async (params) => {
        console.log(`✈️ [AGENT DELEGATION] Delegating to flight-researcher: ${params.task}`);
        
        // This would normally delegate to the actual flight-researcher agent
        // For now, we'll simulate the delegation
        const delegationResult = {
            agentId: 'flight-researcher',
            task: params.task,
            status: 'delegated',
            parameters: params,
            timestamp: new Date().toISOString(),
            expectedCompletion: '10-15 minutes',
            message: `Task "${params.task}" has been delegated to the flight researcher agent. The agent will search for flights from ${params.origin} to ${params.destination} on ${params.departureDate}.`
        };
        
        return JSON.stringify(delegationResult, null, 2);
    }
};

export const hotelBookerTool = {
    name: 'hotel-booker',
    description: 'Delegate hotel search and booking tasks to the specialized hotel booker agent.',
    schema: {
        function_declaration: {
            name: 'hotel-booker',
            description: 'Request the hotel booker agent to find and suggest accommodation options.',
            parameters: {
                type: 'OBJECT',
                properties: {
                    task: {
                        type: 'STRING',
                        description: 'Specific task for the hotel booker (e.g., "find_hotels", "compare_options", "check_availability")'
                    },
                    location: {
                        type: 'STRING',
                        description: 'City or area for accommodation'
                    },
                    checkInDate: {
                        type: 'STRING',
                        description: 'Check-in date in YYYY-MM-DD format'
                    },
                    checkOutDate: {
                        type: 'STRING',
                        description: 'Check-out date in YYYY-MM-DD format'
                    },
                    guests: {
                        type: 'NUMBER',
                        description: 'Number of guests'
                    },
                    rooms: {
                        type: 'NUMBER',
                        description: 'Number of rooms needed'
                    },
                    budget: {
                        type: 'NUMBER',
                        description: 'Maximum budget per night'
                    },
                    preferences: {
                        type: 'STRING',
                        description: 'Hotel preferences (star rating, amenities, location, etc.)'
                    }
                },
                required: ['task', 'location', 'checkInDate', 'checkOutDate']
            }
        }
    },
    call: async (params) => {
        console.log(`🏨 [AGENT DELEGATION] Delegating to hotel-booker: ${params.task}`);
        
        const delegationResult = {
            agentId: 'hotel-booker',
            task: params.task,
            status: 'delegated',
            parameters: params,
            timestamp: new Date().toISOString(),
            expectedCompletion: '8-12 minutes',
            message: `Task "${params.task}" has been delegated to the hotel booker agent. The agent will search for accommodation in ${params.location} from ${params.checkInDate} to ${params.checkOutDate}.`
        };
        
        return JSON.stringify(delegationResult, null, 2);
    }
};

export const itineraryBuilderTool = {
    name: 'itinerary-builder',
    description: 'Delegate itinerary creation tasks to the specialized itinerary builder agent.',
    schema: {
        function_declaration: {
            name: 'itinerary-builder',
            description: 'Request the itinerary builder agent to create detailed daily plans and activity suggestions.',
            parameters: {
                type: 'OBJECT',
                properties: {
                    task: {
                        type: 'STRING',
                        description: 'Specific task for the itinerary builder (e.g., "create_itinerary", "find_attractions", "plan_activities")'
                    },
                    location: {
                        type: 'STRING',
                        description: 'Destination city or region'
                    },
                    duration: {
                        type: 'NUMBER',
                        description: 'Number of days for the itinerary'
                    },
                    interests: {
                        type: 'STRING',
                        description: 'Traveler interests and preferences'
                    },
                    budget: {
                        type: 'NUMBER',
                        description: 'Budget for activities and dining'
                    },
                    flightInfo: {
                        type: 'STRING',
                        description: 'Flight information to coordinate timing'
                    },
                    hotelInfo: {
                        type: 'STRING',
                        description: 'Hotel information for location context'
                    },
                    specialRequests: {
                        type: 'STRING',
                        description: 'Any special requests or requirements'
                    }
                },
                required: ['task', 'location', 'duration', 'interests']
            }
        }
    },
    call: async (params) => {
        console.log(`🗺️ [AGENT DELEGATION] Delegating to itinerary-builder: ${params.task}`);
        
        const delegationResult = {
            agentId: 'itinerary-builder',
            task: params.task,
            status: 'delegated',
            parameters: params,
            timestamp: new Date().toISOString(),
            expectedCompletion: '15-20 minutes',
            message: `Task "${params.task}" has been delegated to the itinerary builder agent. The agent will create a ${params.duration}-day itinerary for ${params.location} based on interests: ${params.interests}.`
        };
        
        return JSON.stringify(delegationResult, null, 2);
    }
};

// Export all delegation tools
export const agentDelegationTools = {
    'flight-researcher': flightResearcherTool,
    'hotel-booker': hotelBookerTool,
    'itinerary-builder': itineraryBuilderTool
};
