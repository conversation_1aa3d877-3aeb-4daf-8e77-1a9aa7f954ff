// test_agent_dispatcher.js - Test the agent-dispatcher tool
import { agentDelegationTools } from './tools/agent_delegation_tools.js';

async function testAgentDispatcher() {
    console.log('🧪 Testing Agent Dispatcher Tool\n');
    
    const agentDispatcher = agentDelegationTools['agent-dispatcher'];
    
    // Test 1: Dispatch to prompt-researcher
    console.log('📋 Test 1: Dispatching research task to prompt-researcher');
    const researchTask = {
        targetAgent: 'prompt-researcher',
        task: 'research_topic',
        inputs: {
            prompt: 'Find information about the latest trends in artificial intelligence and machine learning for 2024'
        },
        priority: 'high',
        expectedOutput: 'Comprehensive research report with key findings and sources'
    };
    
    try {
        const result1 = await agentDispatcher.call(researchTask);
        console.log('✅ Research dispatch result:');
        console.log(result1);
        console.log('\n' + '='.repeat(60) + '\n');
    } catch (error) {
        console.error('❌ Error in research dispatch:', error);
    }
    
    // Test 2: Dispatch to writer
    console.log('📋 Test 2: Dispatching writing task to writer');
    const writingTask = {
        targetAgent: 'writer',
        task: 'draft_content',
        inputs: {
            research: 'Research findings: AI trends include generative AI, multimodal models, and edge computing...'
        },
        priority: 'medium',
        expectedOutput: 'Well-structured blog post draft'
    };
    
    try {
        const result2 = await agentDispatcher.call(writingTask);
        console.log('✅ Writing dispatch result:');
        console.log(result2);
        console.log('\n' + '='.repeat(60) + '\n');
    } catch (error) {
        console.error('❌ Error in writing dispatch:', error);
    }
    
    // Test 3: Dispatch to refiner
    console.log('📋 Test 3: Dispatching refinement task to refiner');
    const refinementTask = {
        targetAgent: 'refiner',
        task: 'polish_content',
        inputs: {
            draft: 'Draft blog post about AI trends with sections on generative AI, multimodal models...'
        },
        priority: 'low',
        expectedOutput: 'Polished, publication-ready blog post'
    };
    
    try {
        const result3 = await agentDispatcher.call(refinementTask);
        console.log('✅ Refinement dispatch result:');
        console.log(result3);
        console.log('\n' + '='.repeat(60) + '\n');
    } catch (error) {
        console.error('❌ Error in refinement dispatch:', error);
    }
    
    // Test 4: Invalid agent (error case)
    console.log('📋 Test 4: Testing error handling with invalid agent');
    const invalidTask = {
        targetAgent: 'invalid-agent',
        task: 'some_task',
        inputs: {
            prompt: 'test'
        }
    };
    
    try {
        const result4 = await agentDispatcher.call(invalidTask);
        console.log('✅ Error handling result:');
        console.log(result4);
    } catch (error) {
        console.error('❌ Unexpected error:', error);
    }
}

// Run the test
testAgentDispatcher().catch(console.error);
